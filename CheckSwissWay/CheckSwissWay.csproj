<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
        <PackageReference Include="PostmarkDotNet" Version="4.4.0" />
        <PackageReference Include="Serilog" Version="4.1.0" />
        <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.Development.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>
